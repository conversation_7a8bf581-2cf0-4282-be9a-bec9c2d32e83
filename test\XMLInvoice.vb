﻿Imports System.Xml
Imports System.Text

Public Class XMLInvoice
    Private xmlDoc As XmlDocument
    Private rootNode As XmlElement
    Private nsManager As XmlNamespaceManager

    Public Sub New()
        xmlDoc = New XmlDocument()
        SetupNamespaces()
    End Sub

    Private Sub SetupNamespaces()
        ' Create XML Declaration
        Dim xmlDecl As XmlDeclaration = xmlDoc.CreateXmlDeclaration("1.0", "UTF-8", Nothing)
        xmlDoc.AppendChild(xmlDecl)

        ' Create root element with namespaces
        rootNode = xmlDoc.CreateElement("Invoice")
        xmlDoc.AppendChild(rootNode)

        ' Add namespaces
        rootNode.SetAttribute("xmlns", "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2")
        rootNode.SetAttribute("xmlns:cac", "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2")
        rootNode.SetAttribute("xmlns:cbc", "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2")
        rootNode.SetAttribute("xmlns:ext", "urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2")

        ' Setup namespace manager
        nsManager = New XmlNamespaceManager(xmlDoc.NameTable)
        nsManager.AddNamespace("", "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2")
        nsManager.AddNamespace("cac", "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2")
        nsManager.AddNamespace("cbc", "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2")
        nsManager.AddNamespace("ext", "urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2")
    End Sub

    Public Sub GenerateInvoice(invoiceData As InvoiceData)
        ' Add basic invoice information
        AddBasicComponents()

        ' Add supplier information
        AddSupplierInformation(invoiceData.SupplierInfo)

        ' Add customer information
        AddCustomerInformation(invoiceData.CustomerInfo)

        ' Add delivery information
        AddDeliveryInformation(invoiceData.DeliveryDate)

        ' Add payment means
        AddPaymentMeans()

        ' Add allowance charge
        AddAllowanceCharge(invoiceData.Discount)

        ' Add tax totals
        AddTaxTotals(invoiceData.TaxableAmount, invoiceData.TaxAmount)

        ' Add monetary totals
        AddMonetaryTotals(invoiceData.Totals)

        ' Add invoice lines
        AddInvoiceLines(invoiceData.InvoiceLines)
    End Sub

    Private Sub AddBasicComponents()
        AddElement("cbc:ProfileID", "reporting:1.0")
        AddElement("cbc:ID", "123456")
        AddElement("cbc:UUID", System.Guid.NewGuid().ToString())
        AddElement("cbc:IssueDate", DateTime.Now.ToString("yyyy-MM-dd"))
        AddElement("cbc:IssueTime", DateTime.Now.ToString("HH:mm:ss"))

        Dim typeCode As XmlElement = xmlDoc.CreateElement("cbc:InvoiceTypeCode")
        typeCode.SetAttribute("name", "0100001")
        typeCode.InnerText = "388"
        rootNode.AppendChild(typeCode)

        AddElement("cbc:DocumentCurrencyCode", "SAR")
        AddElement("cbc:TaxCurrencyCode", "SAR")
    End Sub

    Private Sub AddSupplierInformation(supplierInfo As SupplierInfo)
        Dim supplierParty As XmlElement = xmlDoc.CreateElement("cac:AccountingSupplierParty")
        Dim party As XmlElement = xmlDoc.CreateElement("cac:Party")

        ' Add Party Identification
        Dim partyId As XmlElement = xmlDoc.CreateElement("cac:PartyIdentification")
        Dim id As XmlElement = xmlDoc.CreateElement("cbc:ID")
        id.SetAttribute("schemeID", "CRN")
        id.InnerText = supplierInfo.CRN
        partyId.AppendChild(id)
        party.AppendChild(partyId)

        ' Add Postal Address
        Dim address As XmlElement = CreateAddress(supplierInfo.Address)
        party.AppendChild(address)

        ' Add Tax Scheme
        Dim taxScheme As XmlElement = CreatePartyTaxScheme(supplierInfo.VATNumber)
        party.AppendChild(taxScheme)

        ' Add Legal Entity
        Dim legalEntity As XmlElement = xmlDoc.CreateElement("cac:PartyLegalEntity")
        AddElement(legalEntity, "cbc:RegistrationName", supplierInfo.Name)
        party.AppendChild(legalEntity)

        supplierParty.AppendChild(party)
        rootNode.AppendChild(supplierParty)
    End Sub

    Private Sub AddCustomerInformation(customerInfo As CustomerInfo)
        Dim customerParty As XmlElement = xmlDoc.CreateElement("cac:AccountingCustomerParty")
        Dim party As XmlElement = xmlDoc.CreateElement("cac:Party")

        ' Add Postal Address
        Dim address As XmlElement = CreateAddress(customerInfo.Address)
        party.AppendChild(address)

        ' Add Tax Scheme
        Dim taxScheme As XmlElement = CreatePartyTaxScheme(customerInfo.VATNumber)
        party.AppendChild(taxScheme)

        ' Add Legal Entity
        Dim legalEntity As XmlElement = xmlDoc.CreateElement("cac:PartyLegalEntity")
        AddElement(legalEntity, "cbc:RegistrationName", customerInfo.Name)
        party.AppendChild(legalEntity)

        customerParty.AppendChild(party)
        rootNode.AppendChild(customerParty)
    End Sub

    Private Function CreateAddress(address As AddressInfo) As XmlElement
        Dim postalAddress As XmlElement = xmlDoc.CreateElement("cac:PostalAddress")

        AddElement(postalAddress, "cbc:StreetName", address.StreetName)
        AddElement(postalAddress, "cbc:BuildingNumber", address.BuildingNumber)
        AddElement(postalAddress, "cbc:CitySubdivisionName", address.CitySubdivision)
        AddElement(postalAddress, "cbc:CityName", address.City)
        AddElement(postalAddress, "cbc:PostalZone", address.PostalCode)

        Dim country As XmlElement = xmlDoc.CreateElement("cac:Country")
        AddElement(country, "cbc:IdentificationCode", address.CountryCode)
        postalAddress.AppendChild(country)

        Return postalAddress
    End Function

    Private Function CreatePartyTaxScheme(vatNumber As String) As XmlElement
        Dim partyTaxScheme As XmlElement = xmlDoc.CreateElement("cac:PartyTaxScheme")
        AddElement(partyTaxScheme, "cbc:CompanyID", vatNumber)

        Dim taxScheme As XmlElement = xmlDoc.CreateElement("cac:TaxScheme")
        AddElement(taxScheme, "cbc:ID", "VAT")
        partyTaxScheme.AppendChild(taxScheme)

        Return partyTaxScheme
    End Function

    Private Sub AddElement(tagName As String, value As String)
        Dim element As XmlElement = xmlDoc.CreateElement(tagName)
        element.InnerText = value
        rootNode.AppendChild(element)
    End Sub

    Private Sub AddElement(parent As XmlElement, tagName As String, value As String)
        Dim element As XmlElement = xmlDoc.CreateElement(tagName)
        element.InnerText = value
        parent.AppendChild(element)
    End Sub

    Public Sub SaveToFile(filePath As String)
        xmlDoc.Save(filePath)
    End Sub
End Class

Public Class InvoiceData
    Public Property SupplierInfo As SupplierInfo
    Public Property CustomerInfo As CustomerInfo
    Public Property DeliveryDate As DateTime
    Public Property Discount As Decimal
    Public Property TaxableAmount As Decimal
    Public Property TaxAmount As Decimal
    Public Property Totals As MonetaryTotal
    Public Property InvoiceLines As List(Of InvoiceLine)
End Class

Public Class SupplierInfo
    Public Property CRN As String
    Public Property Name As String
    Public Property VATNumber As String
    Public Property Address As AddressInfo
End Class

Public Class CustomerInfo
    Public Property Name As String
    Public Property VATNumber As String
    Public Property Address As AddressInfo
End Class

Public Class AddressInfo
    Public Property StreetName As String
    Public Property BuildingNumber As String
    Public Property CitySubdivision As String
    Public Property City As String
    Public Property PostalCode As String
    Public Property CountryCode As String
End Class

Public Class MonetaryTotal
    Public Property LineExtensionAmount As Decimal
    Public Property TaxExclusiveAmount As Decimal
    Public Property TaxInclusiveAmount As Decimal
    Public Property AllowanceTotalAmount As Decimal
    Public Property PayableAmount As Decimal
End Class

Public Class InvoiceLine
    Public Property ID As Integer
    Public Property Quantity As Decimal
    Public Property UnitCode As String = "PCE"
    Public Property LineExtensionAmount As Decimal
    Public Property TaxAmount As Decimal
    Public Property RoundingAmount As Decimal
    Public Property ItemName As String
    Public Property PriceAmount As Decimal
End Class